<?php

namespace Config;

use CodeIgniter\Router\RouteCollection;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();

// Auto-routing configuration
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
$routes->setAutoRoute(false); // Setting to false for security

// For improved auto-routing, uncomment the following line
// $routes->setAutoRoute(true);
// And ensure $autoRoutesImproved is set to true in app/Config/Feature.php

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');

// Authentication Routes - RESTful approach
$routes->get('login', 'Home::login');                    // Display login form
$routes->post('login', 'Home::processLogin');            // Process login form submission
$routes->get('logout', 'Home::logout');                  // Logout user
$routes->get('about', 'Home::about');
$routes->get('findme/(:any)', 'Home::findme/$1');
$routes->post('gofindme', 'Home::gofindme');
$routes->post('open_profile', 'Home::open_profile');

// Dashboard Routes
$routes->get('dashboard', 'Admindash::index');

// Mapping Routes
$routes->get('mapping', 'Mapping::index');

// Dakoii Routes
$routes->group('dakoii', function($routes) {
    $routes->get('', 'DakoiiAuth::index');
    $routes->get('login', 'DakoiiAuth::login');
    $routes->post('login', 'DakoiiAuth::processLogin');
    $routes->get('logout', 'DakoiiAuth::logout');
    $routes->get('dashboard', 'DakoiiDashboard::index');

    // Data Management Routes
    $routes->group('data', function($routes) {
        // Main data dashboard
        $routes->get('/', 'DakoiiData::index');

        // Crops management routes - RESTful routes
        $routes->get('crops', 'DakoiiData::index_crops');
        $routes->get('crops/create', 'DakoiiData::create_crop');
        $routes->post('crops', 'DakoiiData::store_crop');
        $routes->get('crops/(:num)', 'DakoiiData::show_crop/$1');
        $routes->get('crops/(:num)/edit', 'DakoiiData::edit_crop/$1');
        $routes->put('crops/(:num)', 'DakoiiData::update_crop/$1');
        $routes->patch('crops/(:num)', 'DakoiiData::update_crop/$1');
        $routes->delete('crops/(:num)', 'DakoiiData::destroy_crop/$1');
        // Form compatibility routes (using method spoofing)
        $routes->post('crops/(:num)', 'DakoiiData::update_crop_form/$1');
        $routes->post('crops/(:num)/delete', 'DakoiiData::destroy_crop_form/$1');

        // Fertilizers management routes
        $routes->get('fertilizers', 'DakoiiData::fertilizers');
        $routes->post('fertilizers/store', 'DakoiiData::storeFertilizer');
        $routes->post('fertilizers/update/(:num)', 'DakoiiData::updateFertilizer/$1');
        $routes->post('fertilizers/delete/(:num)', 'DakoiiData::deleteFertilizer/$1');

        // Pesticides management routes
        $routes->get('pesticides', 'DakoiiData::pesticides');
        $routes->post('pesticides/store', 'DakoiiData::storePesticide');
        $routes->post('pesticides/update/(:num)', 'DakoiiData::updatePesticide/$1');
        $routes->post('pesticides/delete/(:num)', 'DakoiiData::deletePesticide/$1');

        // Infections management routes
        $routes->get('infections', 'DakoiiData::infections');
        $routes->post('infections/store', 'DakoiiData::storeInfection');
        $routes->post('infections/update/(:num)', 'DakoiiData::updateInfection/$1');
        $routes->post('infections/delete/(:num)', 'DakoiiData::deleteInfection/$1');

        // Livestock management routes - Simple RESTful
        $routes->get('livestock', 'DakoiiData::livestock');
        $routes->get('livestock/create', 'DakoiiData::createLivestock');
        $routes->post('livestock/store', 'DakoiiData::storeLivestock');
        $routes->get('livestock/edit/(:num)', 'DakoiiData::editLivestock/$1');
        $routes->post('livestock/update/(:num)', 'DakoiiData::updateLivestock/$1');
        $routes->get('livestock/delete/(:num)', 'DakoiiData::deleteLivestock/$1');

        // Education management routes
        $routes->get('education', 'DakoiiData::education');
        $routes->post('education/store', 'DakoiiData::storeEducation');
        $routes->post('education/update/(:num)', 'DakoiiData::updateEducation/$1');
        $routes->post('education/delete/(:num)', 'DakoiiData::deleteEducation/$1');
    });

    // RESTful Organization Routes using ID instead of orgcode
    $routes->group('organizations', function($routes) {
        $routes->get('/', 'DakoiiOrganizations::index');
        $routes->get('create', 'DakoiiOrganizations::create');
        $routes->post('store', 'DakoiiOrganizations::store');
        $routes->get('show/(:num)', 'DakoiiOrganizations::show/$1');
        $routes->get('edit/(:num)', 'DakoiiOrganizations::edit/$1');
        $routes->post('update/(:num)', 'DakoiiOrganizations::update/$1');
        $routes->post('update-license/(:num)', 'DakoiiOrganizations::updateLicense/$1');
        $routes->post('delete/(:num)', 'DakoiiOrganizations::delete/$1');

        // Organization-specific admin routes
        $routes->get('(:num)/admins', 'DakoiiSystemAdmins::orgAdmins/$1');
        $routes->get('(:num)/admins/create', 'DakoiiSystemAdmins::createForOrg/$1');
        $routes->post('(:num)/admins/store', 'DakoiiSystemAdmins::storeForOrg/$1');
    });

    // RESTful System Administrators Routes
    $routes->group('system-admins', function($routes) {
        $routes->get('/', 'DakoiiSystemAdmins::index');
        $routes->get('create', 'DakoiiSystemAdmins::create');
        $routes->post('store', 'DakoiiSystemAdmins::store');
        $routes->get('show/(:num)', 'DakoiiSystemAdmins::show/$1');
        $routes->get('edit/(:num)', 'DakoiiSystemAdmins::edit/$1');
        $routes->post('update/(:num)', 'DakoiiSystemAdmins::update/$1');
        $routes->post('delete/(:num)', 'DakoiiSystemAdmins::delete/$1');
    });

    // RESTful Dakoii Users Routes (for dakoii portal users - separate from organization users)
    $routes->group('users', function($routes) {
        $routes->get('/', 'DakoiiUsers::index');
        $routes->get('create', 'DakoiiUsers::create');
        $routes->post('store', 'DakoiiUsers::store');
        $routes->get('show/(:num)', 'DakoiiUsers::show/$1');
        $routes->get('edit/(:num)', 'DakoiiUsers::edit/$1');
        $routes->post('update/(:num)', 'DakoiiUsers::update/$1');
        $routes->post('delete/(:num)', 'DakoiiUsers::delete/$1');
    });

    // RESTful Locations Management Routes
    $routes->group('locations', function($routes) {
        // Main locations dashboard
        $routes->get('/', 'DakoiiLocations::index');

        // Countries CRUD
        $routes->get('countries', 'DakoiiLocations::countries');
        $routes->get('countries/create', 'DakoiiLocations::createCountry');
        $routes->post('countries/store', 'DakoiiLocations::storeCountry');
        $routes->get('countries/(:num)/edit', 'DakoiiLocations::editCountry/$1');
        $routes->post('countries/(:num)/update', 'DakoiiLocations::updateCountry/$1');
        $routes->post('countries/(:num)/delete', 'DakoiiLocations::deleteCountry/$1');

        // Provinces CRUD
        $routes->get('provinces', 'DakoiiLocations::provinces');
        $routes->get('provinces/create', 'DakoiiLocations::createProvince');
        $routes->post('provinces/store', 'DakoiiLocations::storeProvince');
        $routes->get('provinces/(:num)/edit', 'DakoiiLocations::editProvince/$1');
        $routes->post('provinces/(:num)/update', 'DakoiiLocations::updateProvince/$1');
        $routes->post('provinces/(:num)/delete', 'DakoiiLocations::deleteProvince/$1');

        // Provinces CSV Import
        $routes->get('provinces/import', 'DakoiiLocations::importProvincesForm');
        $routes->post('provinces/import', 'DakoiiLocations::importProvinces');
        $routes->get('provinces/sample-csv', 'DakoiiLocations::downloadProvincesSample');

        // Districts CRUD
        $routes->get('districts/(:num)', 'DakoiiLocations::districts/$1');
        $routes->get('districts/create/(:num)', 'DakoiiLocations::createDistrict/$1');
        $routes->get('districts/create', 'DakoiiLocations::createDistrict');
        $routes->post('districts/store', 'DakoiiLocations::storeDistrict');
        $routes->get('districts/edit/(:num)', 'DakoiiLocations::editDistrict/$1');
        $routes->post('districts/(:num)/update', 'DakoiiLocations::updateDistrict/$1');
        $routes->post('districts/(:num)/delete', 'DakoiiLocations::deleteDistrict/$1');

        // Districts CSV Import
        $routes->get('districts/import/(:num)', 'DakoiiLocations::importDistrictsForm/$1');
        $routes->post('districts/import/(:num)', 'DakoiiLocations::importDistricts/$1');
        $routes->get('districts/sample-csv/(:num)', 'DakoiiLocations::downloadDistrictsSample/$1');

        // LLGs CRUD
        $routes->get('llgs/(:num)', 'DakoiiLocations::llgs/$1');
        $routes->get('llgs/create/(:num)', 'DakoiiLocations::createLlg/$1');
        $routes->get('llgs/create', 'DakoiiLocations::createLlg');
        $routes->post('llgs/store', 'DakoiiLocations::storeLlg');
        $routes->get('llgs/edit/(:num)', 'DakoiiLocations::editLlg/$1');
        $routes->post('llgs/(:num)/update', 'DakoiiLocations::updateLlg/$1');
        $routes->get('llgs/delete/(:num)', 'DakoiiLocations::deleteLlg/$1');

        // LLGs CSV Import
        $routes->get('llgs/import/(:num)', 'DakoiiLocations::importLlgsForm/$1');
        $routes->post('llgs/import/(:num)', 'DakoiiLocations::importLlgs/$1');
        $routes->get('llgs/sample-csv/(:num)', 'DakoiiLocations::downloadLlgsSample/$1');

        // Wards CRUD
        $routes->get('wards/(:num)', 'DakoiiLocations::wards/$1');
        $routes->get('wards/create/(:num)', 'DakoiiLocations::createWard/$1');
        $routes->get('wards/create', 'DakoiiLocations::createWard');
        $routes->post('wards/store', 'DakoiiLocations::storeWard');
        $routes->get('wards/edit/(:num)', 'DakoiiLocations::editWard/$1');
        $routes->post('wards/(:num)/update', 'DakoiiLocations::updateWard/$1');
        $routes->get('wards/delete/(:num)', 'DakoiiLocations::deleteWard/$1');

        // Wards CSV Import
        $routes->get('wards/import/(:num)', 'DakoiiLocations::importWardsForm/$1');
        $routes->post('wards/import/(:num)', 'DakoiiLocations::importWards/$1');
        $routes->get('wards/sample-csv/(:num)', 'DakoiiLocations::downloadWardsSample/$1');
    });
});



// Admin Portal Routes
$routes->group('admin', function($routes) {
    // Admin Authentication Routes (no filter)
    $routes->get('', 'AdminAuth::index');
    $routes->get('login', 'AdminAuth::login');
    $routes->post('login', 'AdminAuth::processLogin');
    $routes->get('logout', 'AdminAuth::logout');

    // Protected Admin Routes
    $routes->group('', ['filter' => 'admin_auth'], function($routes) {
        $routes->get('dashboard', 'AdminDashboard::index');

    // Admin User Management Routes - RESTful
    $routes->group('users', function($routes) {
        $routes->get('/', 'AdminUsers::index');                    // List all organization users
        $routes->get('create', 'AdminUsers::create');              // Show create user form
        $routes->post('/', 'AdminUsers::store');                   // Store new user
        $routes->get('(:num)', 'AdminUsers::show/$1');             // Show user details
        $routes->get('(:num)/edit', 'AdminUsers::edit/$1');        // Show edit user form
        $routes->put('(:num)', 'AdminUsers::update/$1');           // Update user
        $routes->patch('(:num)', 'AdminUsers::update/$1');         // Update user (partial)
        $routes->delete('(:num)', 'AdminUsers::destroy/$1');       // Delete user

        // Form compatibility routes (for standard form submissions)
        $routes->post('(:num)', 'AdminUsers::update_form/$1');     // Update via form
        $routes->post('(:num)/delete', 'AdminUsers::destroy_form/$1'); // Delete via form

        // User permissions management
        $routes->get('(:num)/permissions', 'AdminUsers::permissions/$1');
        $routes->post('(:num)/permissions', 'AdminUsers::updatePermissions/$1');
        $routes->post('(:num)/districts', 'AdminUsers::addDistrictPermission/$1');
        $routes->delete('(:num)/districts/(:num)', 'AdminUsers::removeDistrictPermission/$1/$2');
        $routes->post('(:num)/districts/(:num)/default', 'AdminUsers::setDefaultDistrict/$1/$2');
    });

    // Admin Groups Management Routes - RESTful
    $routes->group('groups', function($routes) {
        $routes->get('/', 'AdminGroups::index');                   // List all groups
        $routes->get('create', 'AdminGroups::create');             // Show create group form
        $routes->post('/', 'AdminGroups::store');                  // Store new group
        $routes->get('(:num)', 'AdminGroups::show/$1');            // Show group details
        $routes->get('(:num)/edit', 'AdminGroups::edit/$1');       // Show edit group form
        $routes->put('(:num)', 'AdminGroups::update/$1');          // Update group
        $routes->patch('(:num)', 'AdminGroups::update/$1');        // Update group (partial)
        $routes->delete('(:num)', 'AdminGroups::destroy/$1');      // Delete group

        // Form compatibility routes
        $routes->post('(:num)', 'AdminGroups::update_form/$1');    // Update via form
        $routes->post('(:num)/delete', 'AdminGroups::destroy_form/$1'); // Delete via form

        // API routes for AJAX calls
        $routes->get('api/list', 'AdminGroups::apiList');          // Get groups for AJAX
        $routes->get('api/(:num)', 'AdminGroups::apiShow/$1');     // Get single group for AJAX
        $routes->get('api/(:num)/path', 'AdminGroups::apiPath/$1'); // Get group path for AJAX
    });

    // Admin Organization Management Routes
    $routes->group('organization', function($routes) {
        $routes->get('/', 'AdminOrganization::index');             // Organization dashboard
        $routes->get('profile', 'AdminOrganization::profile');     // Organization profile
        $routes->get('edit', 'AdminOrganization::edit');           // Edit organization
        $routes->put('/', 'AdminOrganization::update');            // Update organization
        $routes->post('/', 'AdminOrganization::update_form');      // Update via form

        // Organization settings
        $routes->get('settings', 'AdminOrganization::settings');
        $routes->post('settings', 'AdminOrganization::updateSettings');
    });

    // Admin Data Management Routes
    $routes->group('data', function($routes) {
        $routes->get('/', 'AdminData::index');                     // Data dashboard

        // Data validation and quality control
        $routes->get('validation', 'AdminData::validation');       // Data validation dashboard
        $routes->get('validation/farmers', 'AdminData::validateFarmers');
        $routes->get('validation/crops', 'AdminData::validateCrops');
        $routes->get('validation/livestock', 'AdminData::validateLivestock');
        $routes->post('validation/approve/(:segment)/(:num)', 'AdminData::approveData/$1/$2');
        $routes->post('validation/reject/(:segment)/(:num)', 'AdminData::rejectData/$1/$2');

        // Data export
        $routes->get('export', 'AdminData::export');
        $routes->post('export/farmers', 'AdminData::exportFarmers');
        $routes->post('export/crops', 'AdminData::exportCrops');
        $routes->post('export/livestock', 'AdminData::exportLivestock');
    });

    // Admin Reports Routes
    $routes->group('reports', function($routes) {
        $routes->get('/', 'AdminReports::index');                  // Reports dashboard
        $routes->get('farmers', 'AdminReports::farmers');          // Farmers report
        $routes->get('crops', 'AdminReports::crops');              // Crops report
        $routes->get('livestock', 'AdminReports::livestock');      // Livestock report
        $routes->get('performance', 'AdminReports::performance');  // Field user performance
        $routes->get('territories', 'AdminReports::territories');  // Territory coverage

        // Report generation
        $routes->post('generate/(:segment)', 'AdminReports::generate/$1');
        $routes->get('download/(:segment)/(:any)', 'AdminReports::download/$1/$2');
    });

    // Admin Field Users Management Routes
    $routes->group('field-users', function($routes) {
        $routes->get('/', 'AdminFieldUsers::index');               // List field users
        $routes->get('create', 'AdminFieldUsers::create');         // Create field user form
        $routes->post('/', 'AdminFieldUsers::store');              // Store field user
        $routes->get('(:num)', 'AdminFieldUsers::show/$1');        // Show field user
        $routes->get('(:num)/edit', 'AdminFieldUsers::edit/$1');   // Edit field user form
        $routes->put('(:num)', 'AdminFieldUsers::update/$1');      // Update field user
        $routes->delete('(:num)', 'AdminFieldUsers::destroy/$1');  // Delete field user

        // Form compatibility
        $routes->post('(:num)', 'AdminFieldUsers::update_form/$1');
        $routes->post('(:num)/delete', 'AdminFieldUsers::destroy_form/$1');

        // Territory assignments
        $routes->get('(:num)/territories', 'AdminFieldUsers::territories/$1');
        $routes->post('(:num)/territories', 'AdminFieldUsers::assignTerritories/$1');
        $routes->delete('(:num)/territories/(:num)', 'AdminFieldUsers::removeTerritoryAssignment/$1/$2');

        // Performance monitoring
        $routes->get('(:num)/performance', 'AdminFieldUsers::performance/$1');
        $routes->get('(:num)/activities', 'AdminFieldUsers::activities/$1');
    });

    // Admin Settings Routes
    $routes->group('settings', function($routes) {
        $routes->get('/', 'AdminSettings::index');                 // Settings dashboard
        $routes->get('general', 'AdminSettings::general');         // General settings
        $routes->post('general', 'AdminSettings::updateGeneral');
        $routes->get('data-validation', 'AdminSettings::dataValidation');
        $routes->post('data-validation', 'AdminSettings::updateDataValidation');
        $routes->get('notifications', 'AdminSettings::notifications');
        $routes->post('notifications', 'AdminSettings::updateNotifications');
    });
    }); // End protected admin routes
}); // End admin routes group

// Legacy Routes - To be moved to Admin Portal
// Note: These routes should be gradually migrated to the admin portal structure above

// Dashboards Routes - Legacy (Move to admin/reports)
$routes->group('dashboards', ['filter' => 'auth'], function($routes) {
    $routes->get('farmers', 'Dashboards\Farmers_Report::index');
    $routes->get('farmers/profile/(:num)', 'Dashboards\Farmers_dashboard::profile/$1');
    $routes->get('crops/view/(:num)', 'Dashboards\Crops_Dashboard::view/$1');
});

// Specialized Dashboard Routes - Legacy (Move to admin/reports)
$routes->get('crops-pesticides', 'Dashboards\CropsPesticides_Dashboard::index', ['filter' => 'auth']);
$routes->get('crops-harvests', 'Dashboards\CropsHarvests_Dashboard::index', ['filter' => 'auth']);
$routes->get('crops-markets', 'Dashboards\CropsMarket_Dashboard::index', ['filter' => 'auth']);
$routes->get('crops-diseases', 'Dashboards\CropsDiseases_Dashboard::index', ['filter' => 'auth']);
$routes->get('crops-fertilizers', 'Dashboards\CropsFertilizers_Dashboard::index', ['filter' => 'auth']);
$routes->get('crop-farm-blocks-dashboard', 'Dashboards\CropsBlock_Dashboard::dashboard_crops_blocks', ['filter' => 'auth']);
$routes->get('dashboards/crops-block/view/(:num)', 'Dashboards\CropsBlock_Dashboard::view/$1', ['filter' => 'auth']);

// Farmers Dashboard Routes - Legacy (Move to admin/reports)
$routes->group('farmers', ['filter' => 'auth'], function($routes) {
    $routes->get('dashboard', 'Dashboards\Farmers_dashboard::index');
});

// Location Dashboard Routes - Legacy (Move to admin/reports)
$routes->group('location', ['filter' => 'auth'], function($routes) {
    $routes->get('dashboard', 'Dashboards\Location_dashboard::index');
    $routes->post('dashboard/get-districts', 'Dashboards\Location_dashboard::getDistricts');
    $routes->post('dashboard/get-llgs', 'Dashboards\Location_dashboard::getLlgs');
    $routes->post('dashboard/get-wards', 'Dashboards\Location_dashboard::getWards');
    $routes->post('dashboard/get-dashboard-farmers-data', 'Dashboards\Location_dashboard::getDashboardFarmersData');
    $routes->post('dashboard/get-dashboard-crops-data', 'Dashboards\Location_dashboard::getDashboardCropsData');
    $routes->post('dashboard/get-dashboard-livestock-data', 'Dashboards\Location_dashboard::getDashboardLivestockData');
    $routes->post('dashboard/get-dashboard-market-data', 'Dashboards\Location_dashboard::getDashboardMarketData');
    $routes->post('dashboard/get-dashboard-livestock-market-data', 'Dashboards\Location_dashboard::getDashboardLivestockMarketData');
    $routes->post('dashboard/get-dashboard-fertilizer-data', 'Dashboards\Location_dashboard::getDashboardFertilizerData');
    $routes->post('dashboard/get-dashboard-pesticide-data', 'Dashboards\Location_dashboard::getDashboardPesticideData');
    $routes->post('dashboard/get-dashboard-data', 'Dashboards\Location_dashboard::getDashboardData');
});

// Selection Routes
$routes->group('selections', function($routes) {
    $routes->post('add-selection', 'Dakoii::addSelection');
    $routes->post('update-selection', 'Dakoii::updateSelection');
    $routes->get('delete-selection/(:num)', 'Dakoii::deleteSelection/$1');
});

// API Routes
$routes->group('api', function($routes) {
    $routes->post('get_districts', 'Api::get_districts');
    $routes->post('get_llgs', 'Api::get_llgs');
});

// Legacy Reports Routes - To be moved to Admin Portal
$routes->group('reports', ['filter' => 'auth'], function($routes) {
    $routes->get('farmers-report', 'Reports::farmersReport');
    $routes->get('farmer-profile/(:num)', 'Reports::farmerProfile/$1');
    $routes->get('crop-buyers', 'Reports::cropBuyers');
    $routes->get('farm-blocks', 'Reports::farmBlocksReport');
});

// Plans Management Routes
$routes->group('plans-management', ['filter' => 'auth'], function($routes) {
    $routes->get('', 'Plans::index');
    $routes->post('add', 'Plans::add');
    $routes->post('update', 'Plans::update');
    $routes->get('delete/(:num)', 'Plans::delete/$1');

    // Programs routes
    $routes->get('programs/(:num)', 'Plans::programs/$1');
    $routes->post('add-program', 'Plans::add_program');
    $routes->post('update-program', 'Plans::update_program');
    $routes->get('delete-program/(:num)', 'Plans::delete_program/$1');

    // Deliverables & Indicators routes
    $routes->get('programs/deliverables-indicators/(:num)', 'Plans::deliverables_indicators/$1');
    $routes->post('add-deliverable', 'Plans::add_deliverable');
    $routes->post('update-deliverable', 'Plans::update_deliverable');
    $routes->get('delete-deliverable/(:num)', 'Plans::delete_deliverable/$1');
    $routes->post('add-indicator', 'Plans::add_indicator');
    $routes->post('update-indicator', 'Plans::update_indicator');
    $routes->get('delete-indicator/(:num)', 'Plans::delete_indicator/$1');

    // KRAs & KPIs routes
    $routes->get('kras-kpis/(:num)', 'Plans::kras_kpis/$1');
    $routes->post('add-kra', 'Plans::add_kra');
    $routes->post('update-kra', 'Plans::update_kra');
    $routes->get('delete-kra/(:num)', 'Plans::delete_kra/$1');
    $routes->post('add-kpi', 'Plans::add_kpi');
    $routes->post('update-kpi', 'Plans::update_kpi');
    $routes->get('delete-kpi/(:num)', 'Plans::delete_kpi/$1');
});

// Projects Management Routes
$routes->group('projects', ['filter' => 'auth'], function($routes) {
    $routes->get('manage', 'Projects::manage');
    $routes->get('supervise', 'Projects::supervise');
    $routes->post('add-project', 'Projects::add_project');
    $routes->post('update-project', 'Projects::update_project');
    $routes->get('view/(:num)', 'Projects::view/$1');
    $routes->get('delete-project/(:num)', 'Projects::delete_project/$1');
    $routes->get('get-programs', 'Projects::getPrograms');
    $routes->get('phases/(:num)', 'Projects::phases/$1');
    $routes->post('add-phase', 'Projects::addPhase');
    $routes->post('update-phase', 'Projects::updatePhase');
    $routes->get('delete-phase/(:num)', 'Projects::deletePhase/$1');
    $routes->post('update-phase-status', 'Projects::updatePhaseStatus');
    $routes->get('get-llgs', 'Projects::getLLGs');
    $routes->get('get-indicators', 'Projects::getIndicators');
});

// Workplan Management Routes
$routes->group('workplans', ['filter' => 'auth'], function($routes) {
    $routes->get('manage', 'Workplans::manage', ['as' => 'manage_workplan']);
    $routes->get('supervise', 'Workplans::supervise', ['as' => 'supervise_workplan']);
    $routes->post('add-workplan', 'Workplans::add_workplan');
    $routes->post('update-workplan', 'Workplans::update_workplan');
    $routes->get('delete-workplan/(:num)', 'Workplans::delete_workplan/$1');
    $routes->get('get-llgs', 'Workplans::getLLGs');
    $routes->get('get-kpis', 'Workplans::getKPIs');
    $routes->get('manage-activities/(:num)', 'Workplans::manage_activities/$1');
    $routes->post('add-activity', 'Workplans::add_activity');
    $routes->post('update-activity', 'Workplans::update_activity');
    $routes->post('update-activity-status', 'Workplans::update_activity_status');
    $routes->get('delete-activity/(:num)', 'Workplans::delete_activity/$1');
    $routes->get('activities/manage-inputs', 'Workplans::manage_inputs');
    $routes->post('add-input', 'Workplans::add_input');
    $routes->post('update-input', 'Workplans::update_input');
    $routes->get('delete-input/(:num)', 'Workplans::delete_input/$1');
    $routes->get('activities/manage-infrastructure', 'Workplans::manage_infrastructure');
    $routes->post('add-infrastructure', 'Workplans::add_infrastructure');
    $routes->post('update-infrastructure', 'Workplans::update_infrastructure');
    $routes->get('delete-infrastructure/(:num)', 'Workplans::delete_infrastructure/$1');
    $routes->get('get-activities', 'Workplans::get_activities');

    // Training routes
    $routes->get('activities/manage-trainings', 'Workplans::manage_trainings');
    $routes->post('add-training', 'Workplans::add_training');
    $routes->post('update-training', 'Workplans::update_training');
    $routes->get('delete-training/(:num)', 'Workplans::delete_training/$1');

    // Training participants routes
    $routes->get('manage-training-participants/(:num)', 'Workplans::manage_training_participants/$1');
    $routes->post('add-participant', 'Workplans::add_participant');
    $routes->post('update-participant', 'Workplans::update_participant');
    $routes->get('delete-participant/(:num)', 'Workplans::delete_participant/$1');
});

// Weather Routes
$routes->get('weather', 'WeatherController::index');
$routes->get('weather/fetch', 'WeatherController::getWeatherData');

// Staff Module Routes
$routes->group('staff', ['namespace' => 'App\Controllers\Staff', 'filter' => 'auth'], function ($routes) {
    // Base Staff Routes
    $routes->get('/', 'Staff::index');
    $routes->get('switch_district/(:num)', 'Staff::switch_district/$1');
    $routes->get('data-entry', 'Staff::dataEntry');
    $routes->get('maps', 'Staff::maps');
    $routes->get('tasks', 'Staff::tasks');

    // Data Entry Routes
    $routes->post('submit-entry', 'Staff::submitEntry');
    $routes->post('upload-data', 'Staff::uploadData');

    // Maps Routes
    $routes->get('get-map-data', 'Staff::getMapData');

    // Tasks Routes
    $routes->post('add-task', 'Staff::addTask');
    $routes->post('update-task', 'Staff::updateTask');
    $routes->get('delete-task/(:num)', 'Staff::deleteTask/$1');

    // Buyer Routes
    $routes->get('buyers', 'Staff::buyers');
    $routes->post('add-buyer', 'Staff::addBuyer');
    $routes->post('update-buyer', 'Staff::updateBuyer');

    // Farmers Management - RESTful Routes
    $routes->get('farmers', 'FarmerController::index');                    // GET /staff/farmers
    $routes->get('farmers/create', 'FarmerController::create');            // GET /staff/farmers/create
    $routes->post('farmers', 'FarmerController::store');                   // POST /staff/farmers
    $routes->get('farmers/(:num)', 'FarmerController::show/$1');           // GET /staff/farmers/{id}
    $routes->get('farmers/(:num)/edit', 'FarmerController::edit/$1');      // GET /staff/farmers/{id}/edit
    $routes->post('farmers/(:num)', 'FarmerController::update/$1');        // POST /staff/farmers/{id} (form compatibility)
    $routes->put('farmers/(:num)', 'FarmerController::update/$1');         // PUT /staff/farmers/{id}
    $routes->patch('farmers/(:num)', 'FarmerController::update/$1');       // PATCH /staff/farmers/{id}
    $routes->get('farmers/(:num)/delete', 'FarmerController::delete/$1');  // GET /staff/farmers/{id}/delete (link compatibility)
    $routes->delete('farmers/(:num)', 'FarmerController::delete/$1');      // DELETE /staff/farmers/{id}

    // Crop Buyers Management - RESTful Routes
    $routes->get('crop-buyers', 'CropBuyersController::index');                    // GET /staff/crop-buyers
    $routes->get('crop-buyers/create', 'CropBuyersController::create');            // GET /staff/crop-buyers/create
    $routes->post('crop-buyers', 'CropBuyersController::store');                   // POST /staff/crop-buyers
    $routes->get('crop-buyers/(:num)', 'CropBuyersController::show/$1');           // GET /staff/crop-buyers/{id}
    $routes->get('crop-buyers/(:num)/edit', 'CropBuyersController::edit/$1');      // GET /staff/crop-buyers/{id}/edit
    $routes->post('crop-buyers/(:num)', 'CropBuyersController::update/$1');        // POST /staff/crop-buyers/{id} (form compatibility)
    $routes->put('crop-buyers/(:num)', 'CropBuyersController::update/$1');         // PUT /staff/crop-buyers/{id}
    $routes->patch('crop-buyers/(:num)', 'CropBuyersController::update/$1');       // PATCH /staff/crop-buyers/{id}

    // OLD Farm Block Routes - REMOVED (replaced by crops-farm-blocks)
    // These routes have been replaced by the new crops-farm-blocks RESTful CRUD below

    // Crops Farm Blocks Routes - New RESTful CRUD
    $routes->group('crops-farm-blocks', function($routes) {
        $routes->get('/', 'CropsFarmBlocksController::index');                    // GET /staff/crops-farm-blocks
        $routes->get('create', 'CropsFarmBlocksController::create');              // GET /staff/crops-farm-blocks/create
        $routes->post('/', 'CropsFarmBlocksController::store');                   // POST /staff/crops-farm-blocks
        $routes->get('(:num)', 'CropsFarmBlocksController::show/$1');             // GET /staff/crops-farm-blocks/{id}
        $routes->get('(:num)/edit', 'CropsFarmBlocksController::edit/$1');        // GET /staff/crops-farm-blocks/{id}/edit
        $routes->put('(:num)', 'CropsFarmBlocksController::update/$1');           // PUT /staff/crops-farm-blocks/{id}
        $routes->patch('(:num)', 'CropsFarmBlocksController::update/$1');         // PATCH /staff/crops-farm-blocks/{id}
        $routes->delete('(:num)', 'CropsFarmBlocksController::destroy/$1');       // DELETE /staff/crops-farm-blocks/{id}

        // Form compatibility routes (using method spoofing)
        $routes->post('(:num)', 'CropsFarmBlocksController::update/$1');          // POST /staff/crops-farm-blocks/{id} (with _method=PUT)
        $routes->post('(:num)/delete', 'CropsFarmBlocksController::destroy/$1');  // POST /staff/crops-farm-blocks/{id}/delete

    });

    // Crops Data Routes - Simple CRUD
    $routes->group('crops', function($routes) {
        $routes->get('data', 'StaffCropsData::crops_data_index');
        $routes->get('data-show/(:num)', 'StaffCropsData::crops_data_show/$1');
        $routes->get('data-create/(:num)', 'StaffCropsData::crops_data_create/$1');
        $routes->post('data-store', 'StaffCropsData::crops_data_store');
        $routes->get('data-edit/(:num)', 'StaffCropsData::crops_data_edit/$1');
        $routes->post('data-update/(:num)', 'StaffCropsData::crops_data_update/$1');
        $routes->get('data-delete/(:num)', 'StaffCropsData::crops_data_delete/$1');

        // Fertilizer Routes
        $routes->get('fertilizer', 'StaffFertilizer::fertilizer_data');
        $routes->get('fertilizer/view/(:num)', 'StaffFertilizer::view_fertilizer_data/$1');
        $routes->post('fertilizer/add', 'StaffFertilizer::add_fertilizer_data');
        $routes->post('fertilizer/update', 'StaffFertilizer::update_fertilizer_data');
        $routes->get('fertilizer/delete/(:num)', 'StaffFertilizer::delete_fertilizer_data/$1');

        // Pesticides Routes
        $routes->get('pesticides', 'StaffPesticides::pesticides_data');
        $routes->get('pesticides/view/(:num)', 'StaffPesticides::view_pesticides_data/$1');
        $routes->post('pesticides/add', 'StaffPesticides::add_pesticides_data');
        $routes->post('pesticides/update', 'StaffPesticides::update_pesticides_data');
        $routes->get('pesticides/delete/(:num)', 'StaffPesticides::delete_pesticides_data/$1');

        // Harvest Routes
        $routes->get('harvest', 'StaffHarvest::harvest_data');
        $routes->get('harvest/view/(:num)', 'StaffHarvest::view_harvest_data/$1');
        $routes->post('harvest/add', 'StaffHarvest::add_harvest_data');
        $routes->post('harvest/update', 'StaffHarvest::update_harvest_data');
        $routes->get('harvest/delete/(:num)', 'StaffHarvest::delete_harvest_data/$1');

        // Marketing Routes
        $routes->get('marketing', 'StaffMarket::market_data');
        $routes->get('marketing/view/(:num)', 'StaffMarket::view_market_data/$1');
        $routes->post('marketing/add', 'StaffMarket::add_market_data');
        $routes->post('marketing/update', 'StaffMarket::update_market_data');
        $routes->get('marketing/delete/(:num)', 'StaffMarket::delete_market_data/$1');

        // Disease Data CRUD Routes - Clean Implementation
        $routes->get('diseases', 'DiseaseData::index');                            // List all blocks with disease data
        $routes->get('diseases/view/(:num)', 'DiseaseData::view/$1');              // View disease data for specific block
        $routes->get('diseases/create/(:num)', 'DiseaseData::create/$1');          // Show create form for specific block
        $routes->post('diseases/store', 'DiseaseData::store');                     // Store new disease data
        $routes->get('diseases/edit/(:num)', 'DiseaseData::edit/$1');              // Show edit form for specific disease record
        $routes->post('diseases/update/(:num)', 'DiseaseData::update/$1');         // Update specific disease record
        $routes->get('diseases/delete/(:num)', 'DiseaseData::delete/$1');          // Delete specific disease record
    });

    // Livestock Module Routes
    $routes->group('livestock', function($routes) {
        // Livestock Farm Blocks Routes
        $routes->get('farm-blocks', 'Staff_Livestock::farm_blocks');
        $routes->get('get-farm-blocks', 'Staff_Livestock::get_farm_blocks');
        $routes->post('add-farm-block', 'Staff_Livestock::add_farm_block');
        $routes->post('update-farm-block', 'Staff_Livestock::update_farm_block');
        $routes->post('delete-farm-block', 'Staff_Livestock::delete_farm_block');
        $routes->post('get-llgs', 'Staff_Livestock::get_llgs');
        $routes->post('get-wards', 'Staff_Livestock::get_wards');
        $routes->get('farm-data', 'Staff_Livestock::farm_data');
        $routes->get('view-farm-data/(:num)', 'Staff_Livestock::view_farm_data/$1');

        // Livestock Farm Data Routes
        $routes->post('add-livestock-data', 'Staff_Livestock::add_livestock_data');
        $routes->post('update-livestock-data', 'Staff_Livestock::update_livestock_data');
        $routes->post('delete-livestock-data', 'Staff_Livestock::delete_livestock_data');
    });



    // Staff Reports Routes
    $routes->group('reports', function($routes) {
        $routes->get('farmers', 'Staff_Reports::farmers');
        $routes->get('farmer_profile/(:num)', 'Staff_Reports::farmer_profile/$1');
        $routes->get('crop-buyers', 'Staff_Reports::crop_buyers');
        $routes->get('crops', 'Staff_Reports::crops');
        $routes->get('blocks', 'Staff_Reports::blocks');
        $routes->get('block_profile/(:num)', 'Staff_Reports::block_profile/$1');
        $routes->get('diseases', 'Staff_Reports::diseases');
        $routes->get('fertilizer', 'Staff_Reports::fertilizer');
        $routes->get('pesticides', 'Staff_Reports::pesticides');
        $routes->get('harvests', 'Staff_Reports::harvests');
        $routes->get('marketing', 'Staff_Reports::marketing');
        $routes->get('livestock-blocks', 'Staff_Reports::livestock_blocks');
        $routes->get('livestock-data', 'Staff_Reports::livestock_data');
    });

    // Staff Workplan routes
    $routes->group('workplan', function($routes) {
        $routes->get('manage', 'Workplan::manage');
    });

    // Tools Routes
    $routes->group('tools', function($routes) {
        // Climate Data Routes - RESTful approach
        $routes->get('climate-data', 'StaffClimateController::report');                    // Main climate data report
        $routes->get('climate-data/create', 'StaffClimateController::new');                // Create new climate focus location
        $routes->post('climate-data', 'StaffClimateController::create');                   // Store new climate focus location
        $routes->get('climate-data/(:num)', 'StaffClimateController::show/$1');            // View specific climate focus location
        $routes->get('climate-data/(:num)/edit', 'StaffClimateController::edit/$1');       // Edit climate focus location
        $routes->post('climate-data/(:num)', 'StaffClimateController::update/$1');         // Update climate focus location
        $routes->put('climate-data/(:num)', 'StaffClimateController::update/$1');          // Update climate focus location (RESTful)
        $routes->patch('climate-data/(:num)', 'StaffClimateController::update/$1');        // Update climate focus location (RESTful)
        $routes->delete('climate-data/(:num)', 'StaffClimateController::delete/$1');       // Delete climate focus location
    });
});

// Farm Block Files routes - OLD ROUTES REMOVED FOR NEW CRUD IMPLEMENTATION

// Exercises Routes
$routes->group('exercises', function($routes) {
    $routes->get('', 'ExercisesController::index');
    $routes->get('create', 'ExercisesController::create');
    $routes->post('store', 'ExercisesController::store');
    $routes->get('view/(:num)', 'ExercisesController::view/$1');
    $routes->get('edit/(:num)', 'ExercisesController::edit/$1');
    $routes->post('update/(:num)', 'ExercisesController::update/$1');
    $routes->post('updateStatus/(:num)', 'ExercisesController::updateStatus/$1');
    $routes->post('delete/(:num)', 'ExercisesController::delete/$1');
});

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (\is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
